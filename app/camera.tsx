import React, { useState } from 'react';
import { useRouter } from 'expo-router';
import LoadingAnimation from '@/components/LoadingAnimation';
import CaptureView from '@/components/CaptureView';
import { analyzeImagesAsync } from '@/services/analyzeImages';
import { InventoryService } from '@/services/InventoryService';
import { useInventory } from '@/contexts/InventoryContext';
import { InventoryItem } from '@/components/types';

export default function CameraScreen() {
  const router = useRouter();
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const { updateLocalInventory, inventoryItems } = useInventory();

  const handlePhotosTaken = async (photos: string[]) => {
    setIsAnalyzing(true);
    try {
      const ingredients = await analyzeImagesAsync(photos);

      // Convert ingredients to InventoryItem format
      const newItems: InventoryItem[] = ingredients.map((ingredient) => ({
        name: ingredient.name,
        quantity: 1, // Default quantity
      }));

      // Optimistically update local state first
      const updatedInventory = [...inventoryItems];
      const currentTime = Date.now();

      for (const newItem of newItems) {
        const existingItemIndex = updatedInventory.findIndex(
          (i) => i.name.toLowerCase() === newItem.name.toLowerCase()
        );

        if (existingItemIndex >= 0) {
          // Update existing item - only update quantity if current is 0
          if (updatedInventory[existingItemIndex].quantity === 0) {
            updatedInventory[existingItemIndex] = {
              ...updatedInventory[existingItemIndex],
              quantity: newItem.quantity,
            };
          }
        } else {
          // Add new item
          updatedInventory.push({
            ...newItem,
            addedAt: currentTime,
          });
        }
      }

      // Update local state immediately for better UX
      updateLocalInventory(updatedInventory);

      // Navigate back to inventory tab immediately
      router.push('/(tabs)/inventory');

      // Save to backend in the background using batch operation
      InventoryService.addOrUpdateMultipleItems(newItems).catch((error) => {
        console.error('Error saving ingredients to inventory:', error);
        // TODO: Show error message to user and revert optimistic update
      });
    } catch (error) {
      console.error('Error analyzing photos:', error);
      // TODO: Show error message to user
      router.back();
    } finally {
      setIsAnalyzing(false);
    }
  };

  if (isAnalyzing) {
    return (
      <LoadingAnimation
        source={require('../assets/images/gifs/bounce-veggie.gif')}
        message='Analyzing your food items...'
      />
    );
  }

  return <CaptureView onPhotosTaken={handlePhotosTaken} onClose={() => router.push('/(tabs)/inventory')} />;
}