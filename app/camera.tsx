import React, { useState } from 'react';
import { useRouter } from 'expo-router';
import LoadingAnimation from '@/components/LoadingAnimation';
import CaptureView from '@/components/CaptureView';
import { analyzeImagesAsync } from '@/services/analyzeImages';
import { InventoryService } from '@/services/InventoryService';
import { useInventory } from '@/contexts/InventoryContext';
import { InventoryItem } from '@/components/types';

export default function CameraScreen() {
  const router = useRouter();
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const { updateLocalInventory } = useInventory();

  const handlePhotosTaken = async (photos: string[]) => {
    setIsAnalyzing(true);
    try {
      const ingredients = await analyzeImagesAsync(photos);

      // Convert ingredients to InventoryItem format
      const newItems: InventoryItem[] = ingredients.map((ingredient) => ({
        name: ingredient.name,
        quantity: 1, // Default quantity
      }));

      // Update inventory in backend first using batch operation
      await InventoryService.addOrUpdateMultipleItems(newItems);

      // Get the updated inventory from backend
      const updatedInventoryItems = await InventoryService.getUserInventory();

      // Recategorize the entire inventory with LLM
      await InventoryService.categorizeInventory(updatedInventoryItems);

      // Update local state with properly categorized inventory
      updateLocalInventory(updatedInventoryItems);

      // Navigate back to inventory tab
      router.push('/(tabs)/inventory');
    } catch (error) {
      console.error('Error analyzing photos:', error);
      // TODO: Show error message to user
      router.back();
    } finally {
      setIsAnalyzing(false);
    }
  };

  if (isAnalyzing) {
    return (
      <LoadingAnimation
        source={require('../assets/images/gifs/bounce-veggie.gif')}
        message='Analyzing and organizing your food items...'
      />
    );
  }

  return <CaptureView onPhotosTaken={handlePhotosTaken} onClose={() => router.push('/(tabs)/inventory')} />;
}